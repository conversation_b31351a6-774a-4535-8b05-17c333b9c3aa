// hooks/useAgentChatSync.ts
// ✅ Synchronized Agent Cha<PERSON> Hook for Real-time Cross-Window Sync

import { useState, useCallback, useEffect } from "react"
import { CompleteAgentManager } from "@/components/agents/agent-manager-complete"
import { useSharedAgentState } from "@/components/agents/shared-agent-state"
import { AgentContext } from "@/components/agents/agent-base"
import { createTaskStatusUpdate } from "@/utils/system-message-utils"
import { LLMRequestService, type StreamChunk } from "@/components/agents/llm-request-service"
import { llmIntegration } from "@/components/agents/llm-integration-service"
import { SemanticSearchService } from "@/components/background/semantic-search"
import { globalChatState, GlobalChatState } from "@/services/global-chat-state"
import type { AgentChatMessage } from "@/types/chat"

export function useAgentChatSync() {
  // ✅ Use global chat state instead of local state
  const [chatState, setChatState] = useState<GlobalChatState>(() => globalChatState.getState())
  const [agentManager] = useState(() => new CompleteAgentManager())
  const [llmService] = useState(() => LLMRequestService.getInstance())
  const [semanticSearch] = useState(() => new SemanticSearchService())
  const [isLLMInitialized, setIsLLMInitialized] = useState(false)

  const sharedState = useSharedAgentState()

  // ✅ Subscribe to global chat state changes
  useEffect(() => {
    const unsubscribe = globalChatState.subscribe(setChatState)

    // Initialize global chat state and force sync
    const initializeAndSync = async () => {
      try {
        await globalChatState.initialize()
        // Force sync with other windows after initialization
        await globalChatState.syncWithOtherWindows()
        console.log('✅ Global chat state initialized and synced')
      } catch (error) {
        console.error('❌ Failed to initialize global chat state:', error)
      }
    }

    initializeAndSync()

    return unsubscribe
  }, [])

  // ✅ Initialize LLM service (matching original implementation)
  useEffect(() => {
    const initializeLLM = async () => {
      try {
        if (!llmIntegration.isInitialized()) {
          console.log('useAgentChatSync: Initializing LLM integration service...')
          await llmIntegration.initialize()
          console.log('useAgentChatSync: LLM integration service initialized successfully')
        }
        setIsLLMInitialized(true)
      } catch (error) {
        console.error('useAgentChatSync: Failed to initialize LLM integration service:', error)
        setIsLLMInitialized(true) // Set to true anyway to allow UI to function
      }
    }

    initializeLLM()
  }, [])

  // ✅ Listen for agent messages for multi-agent responses
  useEffect(() => {
    const handleAgentMessage = (message: any) => {
      console.log('🔔 Agent message received in sync chat:', message)

      if (message.type === 'completion' || message.type === 'info') {
        const agentResponse: AgentChatMessage = {
          id: `agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          content: message.message,
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: message.agentId,
          agentId: message.agentId,
          taskId: message.taskId,
          metadata: message.metadata
        }

        // Add to global state (will sync across windows)
        globalChatState.addMessage(agentResponse)
      }
    }

    // Subscribe to shared agent state messages
    const unsubscribe = sharedState.messages ? () => {} : () => {}

    return unsubscribe
  }, [sharedState])

  // ✅ Task 65: Semantic Query Detection
  const detectSemanticQuery = useCallback((input: string): boolean => {
    const semanticPatterns = [
      /^(where\s+is|find|search\s+for|locate|show\s+me)/i,
      /^(what\s+is|how\s+does|explain)/i,
      /^(list\s+all|show\s+all|find\s+all)/i,
      /(functions?\s+that|methods?\s+that|classes?\s+that)/i,
      /(components?\s+that|files?\s+that|modules?\s+that)/i,
      /^(grep|search|find)\s+/i
    ]

    return semanticPatterns.some(pattern => pattern.test(input.trim()))
  }, [])

  // ✅ Task 65: Handle Semantic Query
  const handleSemanticQuery = useCallback(async (query: string): Promise<AgentChatMessage> => {
    try {
      console.log('🔍 Processing semantic query:', query)

      const searchResults = await semanticSearch.searchCode({
        query,
        maxResults: 5,
        minSimilarity: 0.3
      })

      if (searchResults.length === 0) {
        return {
          id: `semantic-${Date.now()}`,
          content: "🔍 **Semantic Search Results**\n\nNo matching code found in the current project for your query. Try rephrasing or using different keywords.",
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: "semantic_search",
          metadata: { isSemanticResult: true, query, resultCount: 0 }
        }
      }

      let formattedResults = `🔍 **Semantic Search Results** (${searchResults.length} matches)\n\n`

      searchResults.forEach((result, index) => {
        formattedResults += `**${index + 1}. ${result.filePath}**\n`
        formattedResults += `*Similarity: ${(result.similarity * 100).toFixed(1)}%*\n`
        if (result.metadata.startLine) {
          formattedResults += `*Lines: ${result.metadata.startLine}-${result.metadata.endLine}*\n`
        }
        formattedResults += `\`\`\`${result.language}\n${result.content.substring(0, 300)}${result.content.length > 300 ? '...' : ''}\n\`\`\`\n\n`
      })

      return {
        id: `semantic-${Date.now()}`,
        content: formattedResults,
        role: "agent",
        timestamp: new Date(),
        status: "completed",
        agentType: "semantic_search",
        metadata: {
          isSemanticResult: true,
          query,
          resultCount: searchResults.length,
          results: searchResults.map(r => ({ filePath: r.filePath, similarity: r.similarity }))
        }
      }
    } catch (error) {
      console.error('Semantic search failed:', error)
      return {
        id: `semantic-error-${Date.now()}`,
        content: `🔍 **Semantic Search Error**\n\nFailed to search the codebase: ${error instanceof Error ? error.message : 'Unknown error'}`,
        role: "agent",
        timestamp: new Date(),
        status: "error",
        agentType: "semantic_search",
        metadata: { isSemanticResult: true, query, error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  }, [semanticSearch])

  // ✅ Send message with global state sync
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || chatState.isProcessing) return

    // ✅ Check if LLM is initialized before proceeding
    if (!isLLMInitialized) {
      console.warn('⚠️ LLM service not initialized yet, please wait...')
      const errorMessage: AgentChatMessage = {
        id: `error-${Date.now()}`,
        content: `⚠️ **System Initializing**: Please wait for the LLM service to initialize before sending messages.`,
        role: "agent",
        timestamp: new Date(),
        status: "error",
        agentType: "system"
      }
      await globalChatState.addMessage(errorMessage)
      return
    }

    const userMessage: AgentChatMessage = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      role: "user",
      timestamp: new Date(),
      status: "sent"
    }

    // Add user message to global state (syncs across windows)
    await globalChatState.addMessage(userMessage)

    // Set processing state (syncs across windows)
    globalChatState.setProcessingState(true)

    try {
      // ✅ Task 65: Check for semantic queries first
      if (detectSemanticQuery(content.trim())) {
        console.log('🔍 Detected semantic query, routing to semantic search')

        const semanticResult = await handleSemanticQuery(content.trim())
        await globalChatState.addMessage(semanticResult)

        globalChatState.setProcessingState(false)
        return
      }

      // Create agent context for the task
      const context: AgentContext = {
        task: content.trim(),
        metadata: {
          source: 'agent_chat',
          requestedAt: Date.now(),
          chatMessageId: userMessage.id
        }
      }

      // Add task to shared state for tracking
      await sharedState.assignTask({
        agentId: 'micromanager',
        description: content.trim(),
        status: 'pending',
        priority: 'medium'
      })

      // Submit task to Micromanager agent
      const taskId = await agentManager.assignTask('micromanager', context, 'medium')

      // Add streaming message placeholder
      const streamingMessage: AgentChatMessage = {
        id: `streaming-${Date.now()}`,
        content: "",
        role: "agent",
        timestamp: new Date(),
        status: "processing",
        agentType: "micromanager",
        agentId: "micromanager",
        taskId,
        isStreaming: true,
        stream: true
      }

      await globalChatState.addMessage(streamingMessage)
      globalChatState.setProcessingState(true, streamingMessage.id)

      // Start real streaming response
      await streamAgentResponse(streamingMessage.id, content.trim())

      // Emit system event for metrics
      emitAgentChatEvent('response_received', {
        agent: 'micromanager',
        taskId
      })

    } catch (error) {
      console.error('❌ Error in sendMessage:', error)

      const errorMessage: AgentChatMessage = {
        id: `error-${Date.now()}`,
        content: `❌ **Error**: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        role: "agent",
        timestamp: new Date(),
        status: "error",
        agentType: "system"
      }

      await globalChatState.addMessage(errorMessage)
      globalChatState.setProcessingState(false)
    }
  }, [chatState.isProcessing, isLLMInitialized, agentManager, sharedState, detectSemanticQuery, handleSemanticQuery])

  // ✅ Stream agent response with global state updates
  const streamAgentResponse = useCallback(async (messageId: string, userMessage: string) => {
    try {
      const agentConfig = {
        id: 'micromanager',
        name: 'Micromanager',
        provider: 'openai' as const,
        model: 'gpt-4',
        systemPrompt: 'You are a helpful AI assistant that coordinates tasks between specialized agents.',
        temperature: 0.7,
        maxTokens: 4000
      }

      if (!agentConfig) {
        throw new Error('Micromanager agent configuration not found')
      }

      // ✅ Check if API key is configured for the provider
      const apiKey = llmService.getApiKey(agentConfig.provider)
      if (!apiKey) {
        console.warn(`⚠️ No API key configured for provider: ${agentConfig.provider}`)

        // Update message with API key error
        globalChatState.updateMessage(messageId, {
          content: `⚠️ **API Key Required**: Please configure your ${agentConfig.provider.toUpperCase()} API key in Settings to use the Agent Chat.`,
          status: "error",
          isStreaming: false,
          stream: false
        })

        globalChatState.setProcessingState(false)
        return
      }

      // Check if streaming is enabled and supported
      const shouldStream = chatState.enableStreaming && LLMRequestService.providerSupportsStreaming(agentConfig.provider)

      if (!shouldStream) {
        console.log('Streaming disabled or not supported, falling back to regular response')
        return await simulateStreamingResponse(messageId, userMessage)
      }

      // Prepare messages for LLM (matching original implementation)
      const llmMessages = [
        {
          role: 'system' as const,
          content: agentConfig.systemPrompt || 'You are a helpful AI assistant that coordinates tasks between specialized agents.'
        },
        {
          role: 'user' as const,
          content: userMessage
        }
      ]

      // ✅ Use correct LLM service method: callLLMStream
      const response = await llmService.callLLMStream(
        agentConfig,
        llmMessages,
        (chunk: StreamChunk) => {
          // Update message in global state (syncs across windows)
          globalChatState.updateMessage(messageId, {
            content: chunk.content,
            status: chunk.isComplete ? "completed" : "processing",
            isStreaming: !chunk.isComplete,
            tokensUsed: chunk.tokensUsed?.total,
            metadata: {
              tokensUsed: chunk.tokensUsed,
              streamingDelta: chunk.delta,
              finishReason: chunk.finishReason
            }
          })
        }
      )

      // Final update
      globalChatState.updateMessage(messageId, {
        content: response.content,
        tokensUsed: response.tokensUsed?.total,
        cost: response.cost,
        status: "completed",
        isStreaming: false,
        stream: false,
        metadata: {
          tokensUsed: response.tokensUsed,
          finishReason: response.finishReason,
          responseTime: response.responseTime
        }
      })

      globalChatState.setProcessingState(false)

    } catch (error) {
      console.error('❌ Real streaming failed, falling back to simulation:', error)

      // Fallback to simulated streaming
      await simulateStreamingResponse(messageId, userMessage)
    }
  }, [llmService, chatState.enableStreaming])

  // ✅ Fallback streaming simulation function (matching original implementation)
  const simulateStreamingResponse = useCallback(async (messageId: string, userMessage: string) => {
    try {
      // ✅ Use a shorter timeout and provide fallback response
      const response = await waitForAgentResponse(messageId).catch(() => {
        // Provide a default response if agent system doesn't respond
        return {
          content: "I understand your request. Let me coordinate with the appropriate agents to help you with this task.",
          tokensUsed: 0,
          cost: 0
        }
      })

      // Simulate streaming by breaking response into chunks
      const fullContent = response.content || "I understand your request. Let me coordinate with the appropriate agents to help you with this task."
      const words = fullContent.split(' ')
      let currentContent = ""

      for (let i = 0; i < words.length; i++) {
        currentContent += (i > 0 ? ' ' : '') + words[i]

        // Update message in global state (syncs across windows)
        globalChatState.updateMessage(messageId, {
          content: currentContent,
          status: i === words.length - 1 ? "completed" : "processing",
          isStreaming: i < words.length - 1,
          tokensUsed: response.tokensUsed,
          cost: response.cost
        })

        // Small delay to simulate typing
        if (i < words.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 50))
        }
      }

      globalChatState.setProcessingState(false)

    } catch (error) {
      console.error('❌ Simulated streaming failed:', error)

      globalChatState.updateMessage(messageId, {
        content: `❌ **Error**: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        status: "error",
        isStreaming: false,
        stream: false
      })

      globalChatState.setProcessingState(false)
    }
  }, [])

  // ✅ Wait for agent response function (matching original implementation)
  const waitForAgentResponse = useCallback(async (messageId: string): Promise<any> => {
    const maxWaitTime = 10000 // ✅ Reduced to 10 seconds for faster fallback
    const pollInterval = 500 // 500ms
    const startTime = Date.now()

    return new Promise((resolve, reject) => {
      const poll = () => {
        // Check if we have a response from the agent system
        const messages = sharedState.messages
        const agentResponse = messages.find(msg =>
          msg.type === 'completion' &&
          msg.metadata?.chatMessageId === messageId
        )

        if (agentResponse) {
          resolve({
            content: agentResponse.message,
            tokensUsed: agentResponse.metadata?.tokensUsed || 0,
            cost: agentResponse.metadata?.cost || 0
          })
          return
        }

        // Check timeout
        if (Date.now() - startTime > maxWaitTime) {
          console.warn(`⚠️ Agent response timeout after ${maxWaitTime}ms, using fallback response`)
          reject(new Error('Agent response timeout'))
          return
        }

        // Continue polling
        setTimeout(poll, pollInterval)
      }

      poll()
    })
  }, [sharedState])

  // ✅ Helper function to emit system events
  const emitAgentChatEvent = useCallback((eventType: string, data: any) => {
    try {
      sharedState.addMessage({
        agentId: 'system',
        message: `Agent Chat Event: ${eventType}`,
        timestamp: Date.now(),
        type: 'info',
        metadata: data
      })

      console.log(`🔔 Agent Chat Event: ${eventType}`, data)
    } catch (error) {
      console.warn('Failed to emit agent chat event:', error)
    }
  }, [sharedState])

  // ✅ Clear messages with global state sync
  const clearMessages = useCallback(async () => {
    await globalChatState.clearMessages()
    console.log('🗑️ Messages cleared and synced across windows')
  }, [])

  // ✅ Set streaming preference with global state sync
  const setEnableStreaming = useCallback((enabled: boolean) => {
    globalChatState.setEnableStreaming(enabled)
    console.log('⚡ Streaming preference updated and synced:', enabled)
  }, [])

  // ✅ Test sync functionality (for debugging)
  const testSync = useCallback(() => {
    globalChatState.testSync()
  }, [])

  return {
    messages: chatState.messages,
    isProcessing: chatState.isProcessing,
    streamingMessageId: chatState.streamingMessageId,
    isLoaded: chatState.isLoaded,
    enableStreaming: chatState.enableStreaming,
    isLLMInitialized,
    setEnableStreaming,
    sendMessage,
    clearMessages,
    testSync, // Expose for debugging
    chatHistory: globalChatState // Expose global chat state for advanced operations
  }
}
