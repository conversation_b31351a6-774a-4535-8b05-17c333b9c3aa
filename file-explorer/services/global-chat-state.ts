// services/global-chat-state.ts
// ✅ Global Chat State Service for Real-time Synchronization

import { AgentChatMessage } from '@/types/chat';
import { getChatHistoryService } from './chat-history';

export interface GlobalChatState {
  messages: AgentChatMessage[];
  isProcessing: boolean;
  streamingMessageId: string | null;
  isLoaded: boolean;
  enableStreaming: boolean;
}

type ChatStateListener = (state: GlobalChatState) => void;

class GlobalChatStateService {
  private static instance: GlobalChatStateService;
  private state: GlobalChatState = {
    messages: [],
    isProcessing: false,
    streamingMessageId: null,
    isLoaded: false,
    enableStreaming: true
  };
  private listeners: Set<ChatStateListener> = new Set();
  private chatHistory = getChatHistoryService();
  private initialized = false;

  static getInstance(): GlobalChatStateService {
    if (!GlobalChatStateService.instance) {
      GlobalChatStateService.instance = new GlobalChatStateService();
    }
    return GlobalChatStateService.instance;
  }

  private constructor() {
    this.setupIPCListeners();
  }

  // ✅ Set up IPC listeners for cross-window synchronization
  private setupIPCListeners() {
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      // Listen for chat state updates from other windows
      window.electronAPI.ipc.on('chat-state-update', (newState: GlobalChatState) => {
        console.log('🔄 Received chat state update from another window:', newState);
        this.state = { ...newState };
        this.notifyListeners();
      });

      // Listen for individual message updates
      window.electronAPI.ipc.on('chat-message-added', (message: AgentChatMessage) => {
        console.log('📨 Received new message from another window:', message);
        this.state.messages = [...this.state.messages, message];
        this.notifyListeners();
        // Save to history
        this.chatHistory.saveChatHistory(this.state.messages).catch(console.error);
      });

      // Listen for processing state changes
      window.electronAPI.ipc.on('chat-processing-changed', (isProcessing: boolean, streamingMessageId?: string) => {
        console.log('⚡ Processing state changed:', isProcessing, streamingMessageId);
        this.state.isProcessing = isProcessing;
        this.state.streamingMessageId = streamingMessageId || null;
        this.notifyListeners();
      });

      // Listen for message updates (streaming)
      window.electronAPI.ipc.on('chat-message-updated', (messageId: string, updates: Partial<AgentChatMessage>) => {
        console.log('📝 Message updated:', messageId, updates);
        this.state.messages = this.state.messages.map(msg =>
          msg.id === messageId ? { ...msg, ...updates } : msg
        );
        this.notifyListeners();
      });
    }
  }

  // ✅ Initialize chat state
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Load chat history
      const savedMessages = await this.chatHistory.loadChatHistory();

      if (savedMessages.length === 0) {
        // Add welcome message for new chats
        const welcomeMessage: AgentChatMessage = {
          id: "welcome-1",
          content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: "micromanager"
        };
        this.state.messages = [welcomeMessage];
        await this.chatHistory.saveChatHistory([welcomeMessage]);
      } else {
        this.state.messages = savedMessages;
      }

      this.state.isLoaded = true;
      this.initialized = true;
      this.notifyListeners();
      
      console.log('✅ Global chat state initialized with', this.state.messages.length, 'messages');
    } catch (error) {
      console.error('❌ Failed to initialize global chat state:', error);
      // Fallback to welcome message
      const welcomeMessage: AgentChatMessage = {
        id: "welcome-1",
        content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
        role: "agent",
        timestamp: new Date(),
        status: "completed",
        agentType: "micromanager"
      };
      this.state.messages = [welcomeMessage];
      this.state.isLoaded = true;
      this.initialized = true;
      this.notifyListeners();
    }
  }

  // ✅ Get current state
  getState(): GlobalChatState {
    return { ...this.state };
  }

  // ✅ Subscribe to state changes
  subscribe(listener: ChatStateListener): () => void {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  // ✅ Add message and sync across windows
  async addMessage(message: AgentChatMessage): Promise<void> {
    this.state.messages = [...this.state.messages, message];
    this.notifyListeners();
    
    // Broadcast to other windows
    this.broadcastToOtherWindows('chat-message-added', message);
    
    // Save to history
    await this.chatHistory.saveChatHistory(this.state.messages);
    
    console.log('📨 Message added and synced:', message.id);
  }

  // ✅ Update message (for streaming)
  updateMessage(messageId: string, updates: Partial<AgentChatMessage>): void {
    this.state.messages = this.state.messages.map(msg =>
      msg.id === messageId ? { ...msg, ...updates } : msg
    );
    this.notifyListeners();
    
    // Broadcast to other windows
    this.broadcastToOtherWindows('chat-message-updated', messageId, updates);
    
    console.log('📝 Message updated and synced:', messageId);
  }

  // ✅ Set processing state and sync
  setProcessingState(isProcessing: boolean, streamingMessageId?: string): void {
    this.state.isProcessing = isProcessing;
    this.state.streamingMessageId = streamingMessageId || null;
    this.notifyListeners();
    
    // Broadcast to other windows
    this.broadcastToOtherWindows('chat-processing-changed', isProcessing, streamingMessageId);
    
    console.log('⚡ Processing state changed and synced:', isProcessing);
  }

  // ✅ Set streaming preference
  setEnableStreaming(enabled: boolean): void {
    this.state.enableStreaming = enabled;
    this.notifyListeners();
    
    // Broadcast full state to other windows
    this.broadcastToOtherWindows('chat-state-update', this.state);
  }

  // ✅ Clear messages and sync
  async clearMessages(): Promise<void> {
    const welcomeMessage: AgentChatMessage = {
      id: "welcome-1",
      content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
      role: "agent",
      timestamp: new Date(),
      status: "completed",
      agentType: "micromanager"
    };

    this.state.messages = [welcomeMessage];
    this.state.isProcessing = false;
    this.state.streamingMessageId = null;
    this.notifyListeners();

    // Clear history and save welcome message
    await this.chatHistory.clearChatHistory();
    await this.chatHistory.saveChatHistory([welcomeMessage]);
    
    // Broadcast to other windows
    this.broadcastToOtherWindows('chat-state-update', this.state);
    
    console.log('🗑️ Messages cleared and synced');
  }

  // ✅ Broadcast events to other windows via IPC
  private broadcastToOtherWindows(event: string, ...args: any[]): void {
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      window.electronAPI.ipc.send(event, ...args);
    }
  }

  // ✅ Notify all local listeners
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.getState());
      } catch (error) {
        console.error('❌ Error in chat state listener:', error);
      }
    });
  }

  // ✅ Force sync with other windows
  async syncWithOtherWindows(): Promise<void> {
    this.broadcastToOtherWindows('chat-state-update', this.state);
    console.log('🔄 Forced sync with other windows');
  }
}

// ✅ Export singleton instance
export const globalChatState = GlobalChatStateService.getInstance();
export default globalChatState;
